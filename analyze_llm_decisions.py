#!/usr/bin/env python3
"""
🧠 分析LLM决策逻辑脚本
分析为什么LLM倾向于只请求k=1而不是更多示例
"""

import json
import logging
from collections import Counter, defaultdict

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_llm_decisions():
    """分析LLM的k值决策模式"""
    
    print("🧠 开始分析LLM决策逻辑...")
    print("=" * 60)
    
    # 读取请求数据
    try:
        with open('cache/requests/requests_ace_2005_1060.json', 'r', encoding='utf-8') as f:
            requests_data = json.load(f)
    except FileNotFoundError:
        print("❌ 找不到请求数据文件")
        return
    
    # 统计k值分布
    k_values = [req[2] for req in requests_data]
    k_counter = Counter(k_values)
    
    print("📊 K值分布统计:")
    for k, count in sorted(k_counter.items()):
        percentage = (count / len(k_values)) * 100
        print(f"  k={k}: {count}次 ({percentage:.1f}%)")
    
    print(f"\n📊 总请求数: {len(requests_data)}")
    print(f"📊 平均k值: {sum(k_values) / len(k_values):.2f}")
    
    # 分析description长度与k值的关系
    print("\n🔍 Description长度与k值关系:")
    length_k_relation = defaultdict(list)
    
    for req in requests_data:
        description = req[1]
        k = req[2]
        desc_length = len(description.split())
        length_k_relation[k].append(desc_length)
    
    for k in sorted(length_k_relation.keys()):
        lengths = length_k_relation[k]
        avg_length = sum(lengths) / len(lengths)
        print(f"  k={k}: 平均description长度 {avg_length:.1f} 词")
    
    # 分析description内容模式
    print("\n📝 Description内容模式分析:")
    
    # 按k值分组分析description
    k1_descriptions = [req[1] for req in requests_data if req[2] == 1]
    k3_descriptions = [req[1] for req in requests_data if req[2] == 3]
    
    print(f"\n🔍 k=1的description示例 (共{len(k1_descriptions)}个):")
    for i, desc in enumerate(k1_descriptions[:10]):  # 显示前10个
        print(f"  {i+1}. {desc}")
    
    print(f"\n🔍 k=3的description示例 (共{len(k3_descriptions)}个):")
    for i, desc in enumerate(k3_descriptions[:10]):  # 显示前10个
        print(f"  {i+1}. {desc}")
    
    # 分析关键词模式
    print("\n🔑 关键词模式分析:")
    
    def extract_keywords(descriptions):
        """提取description中的关键词"""
        all_words = []
        for desc in descriptions:
            words = desc.lower().split()
            all_words.extend(words)
        return Counter(all_words)
    
    k1_keywords = extract_keywords(k1_descriptions)
    k3_keywords = extract_keywords(k3_descriptions)
    
    print("\n📊 k=1最常见关键词:")
    for word, count in k1_keywords.most_common(10):
        print(f"  '{word}': {count}次")
    
    print("\n📊 k=3最常见关键词:")
    for word, count in k3_keywords.most_common(10):
        print(f"  '{word}': {count}次")
    
    # 分析实体类型需求
    print("\n🏷️ 实体类型需求分析:")
    
    def count_entity_mentions(descriptions):
        """统计实体类型提及次数"""
        entity_types = ['person', 'organization', 'location', 'date', 'money', 'misc']
        counts = defaultdict(int)
        
        for desc in descriptions:
            desc_lower = desc.lower()
            for entity_type in entity_types:
                if entity_type in desc_lower:
                    counts[entity_type] += 1
        return counts
    
    k1_entities = count_entity_mentions(k1_descriptions)
    k3_entities = count_entity_mentions(k3_descriptions)
    
    print("\n📊 k=1中实体类型提及:")
    for entity, count in sorted(k1_entities.items()):
        print(f"  {entity}: {count}次")
    
    print("\n📊 k=3中实体类型提及:")
    for entity, count in sorted(k3_entities.items()):
        print(f"  {entity}: {count}次")
    
    # 分析复杂度指标
    print("\n🧮 复杂度指标分析:")
    
    def analyze_complexity(descriptions, k_value):
        """分析description的复杂度指标"""
        total_words = 0
        total_entities_mentioned = 0
        total_commas = 0
        total_and_or = 0
        
        for desc in descriptions:
            words = desc.split()
            total_words += len(words)
            total_commas += desc.count(',')
            total_and_or += desc.lower().count(' and ') + desc.lower().count(' or ')
            
            # 统计提及的实体类型数量
            entity_types = ['person', 'organization', 'location', 'date', 'money', 'misc']
            entities_in_desc = sum(1 for et in entity_types if et in desc.lower())
            total_entities_mentioned += entities_in_desc
        
        count = len(descriptions)
        if count == 0:
            return
            
        print(f"\n📊 k={k_value}的复杂度指标:")
        print(f"  平均词数: {total_words / count:.1f}")
        print(f"  平均逗号数: {total_commas / count:.1f}")
        print(f"  平均and/or数: {total_and_or / count:.1f}")
        print(f"  平均提及实体类型数: {total_entities_mentioned / count:.1f}")
    
    analyze_complexity(k1_descriptions, 1)
    analyze_complexity(k3_descriptions, 3)
    
    # 分析决策模式
    print("\n🎯 决策模式总结:")
    
    # 计算单一实体类型vs多实体类型的比例
    single_entity_k1 = sum(1 for desc in k1_descriptions if len([et for et in ['person', 'organization', 'location'] if et in desc.lower()]) <= 1)
    multi_entity_k1 = len(k1_descriptions) - single_entity_k1
    
    single_entity_k3 = sum(1 for desc in k3_descriptions if len([et for et in ['person', 'organization', 'location'] if et in desc.lower()]) <= 1)
    multi_entity_k3 = len(k3_descriptions) - single_entity_k3
    
    print(f"\n📊 实体类型复杂度与k值关系:")
    print(f"  k=1: 单一实体类型 {single_entity_k1}个, 多实体类型 {multi_entity_k1}个")
    print(f"  k=3: 单一实体类型 {single_entity_k3}个, 多实体类型 {multi_entity_k3}个")
    
    if len(k1_descriptions) > 0:
        k1_multi_ratio = multi_entity_k1 / len(k1_descriptions) * 100
        print(f"  k=1中多实体类型比例: {k1_multi_ratio:.1f}%")
    
    if len(k3_descriptions) > 0:
        k3_multi_ratio = multi_entity_k3 / len(k3_descriptions) * 100
        print(f"  k=3中多实体类型比例: {k3_multi_ratio:.1f}%")
    
    print("\n" + "=" * 60)
    print("🧠 分析完成")

if __name__ == "__main__":
    analyze_llm_decisions()
